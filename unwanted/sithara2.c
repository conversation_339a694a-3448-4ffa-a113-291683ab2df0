// A small, beginner-style single-file solution for "Maze Runner (Snake & Ladders)"
// Keeps data/layout simple and straightforward, while implementing the rules in the brief.
// Build:  gcc -std=c99 -O2 -Wall -Wextra -o maze_simple maze_simple.c

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <ctype.h>

/* ---------- constants (plain and upfront) ---------- */
#define FLOORS 3
#define ROWS   10      /* width : 0..9  */
#define COLS   25      /* length: 0..24 */

#define MAX_ROUNDS 1000
#define MP_INIT 100
#define PENALTY_BLOCKED 2
#define STAIR_TOGGLE_EVERY 5

/* start area: floor 0, rows 6..9, cols 8..16 */
#define ST_RMIN 6
#define ST_RMAX 9
#define ST_CMIN 8
#define ST_CMAX 16

/* floor shapes:
   - F0: full 10x25
   - F1: two rectangles (cols 0..7 and 16..23) + bridge rows 6..9 cols 8..16
   - F2: one rectangle cols 8..16 */
#define F1_LEFT_CMIN 0
#define F1_LEFT_CMAX 7
#define F1_RIGHT_CMIN 16
#define F1_RIGHT_CMAX 23
#define F1_BR_RMIN 6
#define F1_BR_RMAX 9
#define F1_BR_CMIN 8
#define F1_BR_CMAX 16

#define F2_CMIN 8
#define F2_CMAX 16

/* Bawana (Rule 7): entrance is [0,9,19]; walls at row 6, col 20; 12 playable cells inside.
   For simplicity we use rows 6..9 and cols 21..23 (4*3 = 12) as the Bawana cells. */
#define BW_F 0
#define BW_RMIN 6
#define BW_RMAX 9
#define BW_CMIN 21
#define BW_CMAX 23
#define BW_ENTR_R 9
#define BW_ENTR_C 19

/* durations */
#define DISORIENTED_TURNS 4
#define TRIGGERED_TURNS   4

/* consumables/bonuses distribution (Rule 10) */
#define PCT_CONS0 25
#define PCT_CONS14 35
#define PCT_ADD12 25
#define PCT_ADD35 10
/* remainder => multipliers x2/x3 */

/* cell kinds */
#define K_VOID  -1  /* outside actual shape */
#define K_FREE   0  /* playable cell */
#define K_START  1  /* start area cells (treated as blocked for walking) */
#define K_BAWANA 2  /* Bawana cells (cannot enter by walking) */
#define K_WALL   3  /* wall/blocked */

/* directions */
#define DIR_N 0
#define DIR_E 1
#define DIR_S 2
#define DIR_W 3

/* stairs mode (global, flips every 5 rounds) */
#define ST_UP   0
#define ST_DOWN 1

/* Bawana cell types */
#define BW_POINTS 0
#define BW_FOOD   1
#define BW_DISOR  2
#define BW_TRIG   3
#define BW_HAPPY  4

/* limits */
#define MAX_STAIRS 512
#define MAX_POLES  512

/* ---------- tiny structs ---------- */
typedef struct {
    char id;         /* 'A','B','C' */
    int f, r, c;     /* position */
    int in_maze;     /* 0=not yet in maze (in start area), 1=in maze */
    int dir;         /* DIR_* */
    int mp;          /* movement points */

    int skip;        /* food poisoning (turns to skip) */
    int disor;       /* disoriented throws left */
    int trig;        /* triggered throws left (double steps) */
    int dirTick;     /* 0..3: roll direction die when dirTick==3 */

    /* start and first-maze-cell per brief */
    int home_r, home_c;
    int first_r, first_c;
    int start_dir;
} Player;

typedef struct { /* stairs are defined from lower floor to upper floor in input */
    int aF,aR,aC; /* lower endpoint */
    int bF,bR,bC; /* upper endpoint */
} Ladder;

typedef struct { /* pole is vertical at (r,c), dropping to lowF if above it */
    int lowF, topF, r, c;
} Pole;

/* ---------- globals (simple arrays) ---------- */
static int shape[FLOORS][ROWS][COLS]; /* K_* */
static int consm[FLOORS][ROWS][COLS]; /* -1 none, else 0..4 */
static int plusp[FLOORS][ROWS][COLS]; /* +1..5 */
static int multp[FLOORS][ROWS][COLS]; /* x1..3 */

static Ladder stairs[MAX_STAIRS];
static int nStairs = 0;
static int stairMode = ST_UP;

static Pole poles[MAX_POLES];
static int nPoles = 0;

/* Bawana mapping for the 12 cells: BW_* type */
static int bawanaType[ROWS][COLS]; /* only BW_RMIN..BW_RMAX, BW_CMIN..BW_CMAX used */

/* players */
static Player P[3];

/* flag */
static int flagF=0, flagR=0, flagC=0;

/* debug counters (optional) */
static int dbg_bawana=0, dbg_stairs=0, dbg_poles=0, dbg_blocks=0;

/* ---------- small helpers ---------- */
static int rnd_int(int a,int b){ return a + rand() % (b - a + 1); }

static int in_bounds(int r,int c){ return r>=0 && r<ROWS && c>=0 && c<COLS; }

static const char* dname(int d){
    switch(d){
        case DIR_N: return "NORTH";
        case DIR_E: return "EAST";
        case DIR_S: return "SOUTH";
        case DIR_W: return "WEST";
        default: return "?";
    }
}
static void step_delta(int d, int *dr, int *dc){
    *dr=0; *dc=0;
    if(d==DIR_N) *dr=-1;
    else if(d==DIR_E) *dc=1;
    else if(d==DIR_S) *dr=1;
    else if(d==DIR_W) *dc=-1;
}
static int dir_from_face(int face){
    if(face==2) return DIR_N;
    if(face==3) return DIR_E;
    if(face==4) return DIR_S;
    if(face==5) return DIR_W;
    return -1; /* 1 or 6 -> keep direction */
}
static int encode_cell(int f,int r,int c){ return f*100 + r*10 + c; }

/* Manhattan distance + floor penalty (choose nearer stair to flag) */
static int stair_distance_score(int f,int r,int c){
    int df = abs(f-flagF);
    int dr = abs(r-flagR);
    int dc = abs(c-flagC);
    return dr + dc + 10*df;
}

/* ---------- input parsing (tolerant to [x,y,z] with commas) ---------- */
static int scan_ints_from_line(const char* s, int *out, int need){
    int cnt=0;
    const char *p=s;
    while(*p && cnt<need){
        while(*p && !(*p=='-' || isdigit((unsigned char)*p))) p++;
        if(!*p) break;
        char *end=NULL;
        long v = strtol(p,&end,10);
        if(p==end) break;
        out[cnt++] = (int)v;
        p=end;
    }
    return cnt;
}

static void load_seed(void){
    FILE *f = fopen("seed.txt","r");
    if(!f){ printf("Can't open seed.txt, using time-based seed\n"); srand((unsigned)time(NULL)); return; }
    long long s=0;
    if(fscanf(f,"%lld",&s)!=1){ printf("Bad seed.txt, using time-based seed\n"); srand((unsigned)time(NULL)); }
    else { srand((unsigned)s); }
    fclose(f);
}
static void load_flag(void){
    FILE *f=fopen("flag.txt","r");
    if(!f){ printf("can't get flag position, Flag Placed Randomly\n"); return; }
    char line[256];
    if(fgets(line,sizeof(line),f)){
        int v[3];
        if(scan_ints_from_line(line,v,3)==3){
            flagF=v[0]; flagR=v[1]; flagC=v[2];
        }
    }
    fclose(f);
}
static void load_stairs(void){
    FILE *f=fopen("stairs.txt","r");
    if(!f){ printf("No stairs.txt, using 0 stairs\n"); return; }
    char line[256];
    while(fgets(line,sizeof(line),f)){
        int v[6];
        if(scan_ints_from_line(line,v,6)==6){
            if(nStairs<MAX_STAIRS){
                stairs[nStairs].aF=v[0]; stairs[nStairs].aR=v[1]; stairs[nStairs].aC=v[2];
                stairs[nStairs].bF=v[3]; stairs[nStairs].bR=v[4]; stairs[nStairs].bC=v[5];
                nStairs++;
            }
        }
    }
    fclose(f);
    printf("Loaded %d stairs\n", nStairs);
}
static void load_poles(void){
    FILE *f=fopen("poles.txt","r");
    if(!f){ printf("No poles.txt, using 0 poles\n"); return; }
    char line[256];
    while(fgets(line,sizeof(line),f)){
        int v[4];
        if(scan_ints_from_line(line,v,4)==4){
            if(nPoles<MAX_POLES){
                int s=v[0], e=v[1];
                poles[nPoles].lowF = (s<e? s:e);
                poles[nPoles].topF = (s<e? e:s);
                poles[nPoles].r = v[2];
                poles[nPoles].c = v[3];
                nPoles++;
            }
        }
    }
    fclose(f);
    printf("Loaded %d poles\n", nPoles);
}
static void draw_wall_segment(int f,int r1,int c1,int r2,int c2){
    if(f<0||f>=FLOORS) return;
    if(r1==r2){
        int cmin = (c1<c2?c1:c2), cmax=(c1>c2?c1:c2);
        for(int c=cmin;c<=cmax;c++) if(in_bounds(r1,c)) shape[f][r1][c]=K_WALL;
    }else if(c1==c2){
        int rmin = (r1<r2?r1:r2), rmax=(r1>r2?r1:r2);
        for(int r=rmin;r<=rmax;r++) if(in_bounds(r,c1)) shape[f][r][c1]=K_WALL;
    }
}
static void load_walls(void){
    FILE *f=fopen("walls.txt","r");
    if(!f){ printf("No walls.txt, using 0 extra walls\n"); return; }
    char line[256];
    int cnt=0;
    while(fgets(line,sizeof(line),f)){
        int v[5];
        if(scan_ints_from_line(line,v,5)==5){
            draw_wall_segment(v[0], v[1],v[2], v[3],v[4]);
            cnt++;
        }
    }
    fclose(f);
    printf("Loaded %d walls\n", cnt);
}

/* ---------- board construction ---------- */
static void build_shapes(void){
    /* set everything to void */
    for(int f=0;f<FLOORS;f++)
        for(int r=0;r<ROWS;r++)
            for(int c=0;c<COLS;c++)
                shape[f][r][c]=K_VOID;

    /* floor 0: full playable */
    for(int r=0;r<ROWS;r++)
    for(int c=0;c<COLS;c++)
        shape[0][r][c]=K_FREE;

    /* start area = blocked for walking */
    for(int r=ST_RMIN;r<=ST_RMAX;r++)
    for(int c=ST_CMIN;c<=ST_CMAX;c++)
        shape[0][r][c]=K_START;

    /* Bawana walls and cells (Rule 7) */
    /* walls: row 6, col 20..24; col 20, rows 6..9 */
    for(int c=20;c<=24;c++) shape[0][6][c]=K_WALL;
    for(int r=6;r<=9;r++)   shape[0][r][20]=K_WALL;

    /* the 12 Bawana cells: rows 6..9, cols 21..23 => K_BAWANA (cannot enter by walking) */
    for(int r=BW_RMIN;r<=BW_RMAX;r++)
    for(int c=BW_CMIN;c<=BW_CMAX;c++){
        shape[BW_F][r][c]=K_BAWANA;
        bawanaType[r][c]=BW_POINTS; /* filled later */
    }

    /* floor 1: left block, right block, and bridge rows */
    for(int r=0;r<ROWS;r++){
        for(int c=F1_LEFT_CMIN;c<=F1_LEFT_CMAX;c++) shape[1][r][c]=K_FREE;
        for(int c=F1_RIGHT_CMIN;c<=F1_RIGHT_CMAX;c++) shape[1][r][c]=K_FREE;
    }
    for(int r=F1_BR_RMIN;r<=F1_BR_RMAX;r++)
        for(int c=F1_BR_CMIN;c<=F1_BR_CMAX;c++)
            shape[1][r][c]=K_FREE;

    /* floor 2: rectangle cols 8..16 */
    for(int r=0;r<ROWS;r++)
        for(int c=F2_CMIN;c<=F2_CMAX;c++)
            shape[2][r][c]=K_FREE;
}

static void clear_effects(void){
    for(int f=0;f<FLOORS;f++)
    for(int r=0;r<ROWS;r++)
    for(int c=0;c<COLS;c++){
        consm[f][r][c] = -1;
        plusp[f][r][c] = 0;
        multp[f][r][c] = 1;
    }
}

static void scatter_effects(void){ /* Rule 10 distribution */
    int cap=FLOORS*ROWS*COLS;
    int (*cells)[3] = malloc(sizeof(int)*3*cap);
    int n=0;
    for(int f=0;f<FLOORS;f++)
    for(int r=0;r<ROWS;r++)
    for(int c=0;c<COLS;c++){
        if(shape[f][r][c]==K_FREE){
            cells[n][0]=f; cells[n][1]=r; cells[n][2]=c; n++;
        }
    }
    int need0 = (PCT_CONS0 * n)/100;
    int need14= (PCT_CONS14 * n)/100;
    int add12 = (PCT_ADD12 * n)/100;
    int add35 = (PCT_ADD35 * n)/100;
    int mul23 = n - (need0+need14+add12+add35);

    int placed=0, tries=0;
    /* helper lambda-ish macro */
    #define PICK_FREE (consm[f][r][c]==-1 && plusp[f][r][c]==0 && multp[f][r][c]==1)

    /* cons 0 */
    placed=0; tries=0;
    while(placed<need0 && tries<10*n){
        int i=rnd_int(0,n-1); int f=cells[i][0],r=cells[i][1],c=cells[i][2];
        if(PICK_FREE){ consm[f][r][c]=0; placed++; }
        tries++;
    }
    /* cons 1..4 */
    placed=0; tries=0;
    while(placed<need14 && tries<10*n){
        int i=rnd_int(0,n-1); int f=cells[i][0],r=cells[i][1],c=cells[i][2];
        if(PICK_FREE){ consm[f][r][c]=rnd_int(1,4); placed++; }
        tries++;
    }
    /* +1..2 */
    placed=0; tries=0;
    while(placed<add12 && tries<10*n){
        int i=rnd_int(0,n-1); int f=cells[i][0],r=cells[i][1],c=cells[i][2];
        if(PICK_FREE){ plusp[f][r][c]=rnd_int(1,2); placed++; }
        tries++;
    }
    /* +3..5 */
    placed=0; tries=0;
    while(placed<add35 && tries<10*n){
        int i=rnd_int(0,n-1); int f=cells[i][0],r=cells[i][1],c=cells[i][2];
        if(PICK_FREE){ plusp[f][r][c]=rnd_int(3,5); placed++; }
        tries++;
    }
    /* x2..x3 */
    placed=0; tries=0;
    while(placed<mul23 && tries<10*n){
        int i=rnd_int(0,n-1); int f=cells[i][0],r=cells[i][1],c=cells[i][2];
        if(PICK_FREE){ multp[f][r][c]= (rnd_int(0,1)?2:3); placed++; }
        tries++;
    }
    free(cells);
    #undef PICK_FREE
}

static void build_bawana_layout(void){ /* 12 cells: two of each special + 4 points(10..100) */
    /* fill everything as points; then set two of each special */
    for(int r=BW_RMIN;r<=BW_RMAX;r++)
        for(int c=BW_CMIN;c<=BW_CMAX;c++)
            bawanaType[r][c]=BW_POINTS;

    int options[12][2]; int m=0;
    for(int r=BW_RMIN;r<=BW_RMAX;r++)
        for(int c=BW_CMIN;c<=BW_CMAX;c++){
            options[m][0]=r; options[m][1]=c; m++;
        }
    /* shuffle */
    for(int i=0;i<m;i++){
        int j=rnd_int(0,m-1);
        int tr=options[i][0], tc=options[i][1];
        options[i][0]=options[j][0]; options[i][1]=options[j][1];
        options[j][0]=tr; options[j][1]=tc;
    }
    /* take first 8 as two each of FOOD,DISOR,TRIG,HAPPY */
    int k=0;
    for(int t=0;t<2;t++){ bawanaType[options[k][0]][options[k][1]]=BW_FOOD; k++; }
    for(int t=0;t<2;t++){ bawanaType[options[k][0]][options[k][1]]=BW_DISOR; k++; }
    for(int t=0;t<2;t++){ bawanaType[options[k][0]][options[k][1]]=BW_TRIG;  k++; }
    for(int t=0;t<2;t++){ bawanaType[options[k][0]][options[k][1]]=BW_HAPPY; k++; }
    /* rest remain BW_POINTS */
}

/* Block the "through" cells for stairs that jump over floor 1 in a beginner-friendly way */
static void apply_stair_through_walls(void){
    for(int i=0;i<nStairs;i++){
        if(abs(stairs[i].aF - stairs[i].bF)==2){
            /* block both endpoints on the middle floor */
            int midF = 1;
            if(in_bounds(stairs[i].aR, stairs[i].aC)) shape[midF][stairs[i].aR][stairs[i].aC]=K_WALL;
            if(in_bounds(stairs[i].bR, stairs[i].bC)) shape[midF][stairs[i].bR][stairs[i].bC]=K_WALL;
        }
    }
}

/* ---------- players & flag ---------- */
static void place_flag_randomly(void){
    if(flagF<0 || flagF>=FLOORS || !in_bounds(flagR,flagC) || shape[flagF][flagR][flagC]!=K_FREE){
        /* pick any free cell */
        int tries=0;
        do{
            flagF=rnd_int(0,FLOORS-1);
            flagR=rnd_int(0,ROWS-1);
            flagC=rnd_int(0,COLS-1);
            tries++;
        }while((shape[flagF][flagR][flagC]!=K_FREE) && tries<100000);
    }
}
static void setup_players(void){
    /* Rule 1 starts */
    /* A -> home (6,12), first (5,12), dir North */
    P[0].id='A'; P[0].f=0; P[0].home_r=6; P[0].home_c=12; P[0].first_r=5; P[0].first_c=12; P[0].start_dir=DIR_N;
    /* B -> home (9,8), first (9,7), dir West */
    P[1].id='B'; P[1].f=0; P[1].home_r=9; P[1].home_c=8;  P[1].first_r=9; P[1].first_c=7;  P[1].start_dir=DIR_W;
    /* C -> home (9,16), first (9,17), dir East */
    P[2].id='C'; P[2].f=0; P[2].home_r=9; P[2].home_c=16; P[2].first_r=9; P[2].first_c=17; P[2].start_dir=DIR_E;

    for(int i=0;i<3;i++){
        P[i].r=P[i].home_r; P[i].c=P[i].home_c; P[i].in_maze=0;
        P[i].dir=P[i].start_dir; P[i].mp=MP_INIT; P[i].skip=0; P[i].disor=0; P[i].trig=0; P[i].dirTick=0;
    }
}

/* ---------- effects while touching cells ---------- */
static void touch_cell_costs(Player *pl){
    int f=pl->f, r=pl->r, c=pl->c;
    if(!in_bounds(r,c)) return;
    int use=consm[f][r][c], add=plusp[f][r][c], mul=multp[f][r][c];
    if(use>=0) pl->mp -= use;
    if(add>0)  pl->mp += add;
    if(mul>1)  pl->mp = pl->mp * mul;
}

/* ---------- jumps (stairs/poles) ---------- */
static int same_cell(int f1,int r1,int c1,int f2,int r2,int c2){
    return f1==f2 && r1==r2 && c1==c2;
}

static void maybe_take_pole(Player *pl, int verbose){
    for(int i=0;i<nPoles;i++){
        if(pl->f>poles[i].lowF && pl->f<=poles[i].topF && pl->r==poles[i].r && pl->c==poles[i].c){
            if(verbose){
                printf("%c lands on %d which is a pole cell. %c slides down and now placed at %d in floor %d\n",
                       pl->id, encode_cell(pl->f,pl->r,pl->c),
                       pl->id, encode_cell(poles[i].lowF,pl->r,pl->c), poles[i].lowF);
                dbg_poles++;
            }
            pl->f=poles[i].lowF;
        }
    }
}

static int choose_stair_dest_if_here(Player *pl, int *outF,int *outR,int *outC){
    /* collect candidate dests if current pos is a stair endpoint according to mode */
    int candF[3], candR[3], candC[3], n=0;
    for(int i=0;i<nStairs;i++){
        int aF=stairs[i].aF,aR=stairs[i].aR,aC=stairs[i].aC;
        int bF=stairs[i].bF,bR=stairs[i].bR,bC=stairs[i].bC;
        if(stairMode==ST_UP){
            if(same_cell(pl->f,pl->r,pl->c,aF,aR,aC)){
                candF[n]=bF; candR[n]=bR; candC[n]=bC; n++;
            }
        }else{ /* ST_DOWN */
            if(same_cell(pl->f,pl->r,pl->c,bF,bR,bC)){
                candF[n]=aF; candR[n]=aR; candC[n]=aC; n++;
            }
        }
        if(n==2) break; /* at most 2 by clarification */
    }
    if(n==0) return 0;
    if(n==1){ *outF=candF[0]; *outR=candR[0]; *outC=candC[0]; return 1; }
    /* pick closer to flag; tie => random */
    int d0=stair_distance_score(candF[0],candR[0],candC[0]);
    int d1=stair_distance_score(candF[1],candR[1],candC[1]);
    int k=(d0<d1?0:(d1<d0?1:rnd_int(0,1)));
    *outF=candF[k]; *outR=candR[k]; *outC=candC[k]; return 1;
}

static void maybe_take_stair(Player *pl, int verbose){
    int df,dr,dc;
    if(choose_stair_dest_if_here(pl,&df,&dr,&dc)){
        if(verbose){
            printf("%c lands on %d which is a stair cell. %c takes the stairs and now placed at %d in floor %d\n",
                   pl->id, encode_cell(pl->f,pl->r,pl->c),
                   pl->id, encode_cell(df,dr,dc), df);
            dbg_stairs++;
        }
        pl->f=df; pl->r=dr; pl->c=dc;
    }
}

/* ---------- Bawana ---------- */
static void place_to_bawana_entrance(Player *pl){
    pl->f=BW_F; pl->r=BW_ENTR_R; pl->c=BW_ENTR_C;
    pl->dir=DIR_N; /* per brief when exiting Bawana place facing North */
}
static void visit_bawana(Player *pl){
    dbg_bawana++;
    /* pick one random Bawana cell and apply its effect */
    int rr=rnd_int(BW_RMIN,BW_RMAX);
    int cc=rnd_int(BW_CMIN,BW_CMAX);
    int typ=bawanaType[rr][cc];

    printf("%c movement points are depleted and requires replenishment. Transporting to Bawana.\n", pl->id);
    printf("%c is placed on a bawana cell and effects take place.\n", pl->id);

    if(typ==BW_FOOD){
        printf("%c eats from Bawana and have a bad case of food poisoning. Will need three rounds to recover.\n", pl->id);
        pl->skip=3;
        /* after 3 rounds, we will visit Bawana again */
    }else if(typ==BW_DISOR){
        printf("%c eats from Bawana and is disoriented and is placed at the entrance of Bawana with 50 movement points.\n", pl->id);
        pl->mp += 50;
        pl->disor = DISORIENTED_TURNS;
        place_to_bawana_entrance(pl);
        pl->in_maze=1;
    }else if(typ==BW_TRIG){
        printf("%c eats from Bawana and is triggered due to bad quality of food. %c is placed at the entrance of Bawana with 50 movement points.\n",
               pl->id, pl->id);
        pl->mp += 50;
        pl->trig = TRIGGERED_TURNS;
        place_to_bawana_entrance(pl);
        pl->in_maze=1;
    }else if(typ==BW_HAPPY){
        printf("%c eats from Bawana and is happy. %c is placed at the entrance of Bawana with 200 movement points.\n",
               pl->id, pl->id);
        pl->mp += 200;
        place_to_bawana_entrance(pl);
        pl->in_maze=1;
    }else{ /* BW_POINTS */
        int gain=rnd_int(10,100);
        printf("%c eats from Bawana and earns %d movement points and is placed at the entrance.\n", pl->id, gain);
        pl->mp += gain;
        place_to_bawana_entrance(pl);
        pl->in_maze=1;
    }
}

/* ---------- walking a full throw (Rule 2 + Rule 4 mid-jumps) ---------- */
/* return 0=blocked(didn't move), 1=moved, 2=MP<=0 and sent to Bawana, 3=loop/stair-pole trap -> sent to start area */
static int walk_exact(Player *pl, int steps){
    int f0=pl->f, r0=pl->r, c0=pl->c, mp0=pl->mp;

    /* choose direction for this throw */
    int used_dir = pl->dir;
    if(pl->disor>0){
        used_dir = rnd_int(0,3); /* random direction overrides direction die */
        printf("%c rolls and %d on the movement dice and is disoriented and move in the %s and moves %d cells and is placed at the ",
               pl->id, (pl->trig? steps/2: steps), dname(used_dir), steps);
    }

    int dr=0, dc=0; step_delta(used_dir,&dr,&dc);

    /* DRY RUN (include mid-step jumps) to ensure full path exists */
    {
        Player sim = *pl;
        int seenCount=0; int seen[64][3]; /* simple loop-detect for jump chain */
        for(int s=0;s<steps;s++){
            int nr = sim.r + dr, nc = sim.c + dc;
            if(!in_bounds(nr,nc)) return 0;
            if(shape[sim.f][nr][nc]!=K_FREE) return 0;
            sim.r=nr; sim.c=nc;
            /* mid-step jumps (simulate quietly) */
            int guard=0;
            while(guard++<64){
                /* record and detect loop */
                for(int t=0;t<seenCount;t++)
                    if(seen[t][0]==sim.f && seen[t][1]==sim.r && seen[t][2]==sim.c) return 3;
                seen[seenCount][0]=sim.f; seen[seenCount][1]=sim.r; seen[seenCount][2]=sim.c; seenCount++;

                maybe_take_pole(&sim,0);
                maybe_take_stair(&sim,0);
                /* if no change, break */
                if(guard>1 && !(same_cell(seen[seenCount-1][0],seen[seenCount-1][1],seen[seenCount-1][2],
                                          sim.f,sim.r,sim.c))) {;}
                else break;
            }
        }
    }

    /* ACTUAL WALK (charge costs, print, jumps, MP depletion check) */
    pl->f=f0; pl->r=r0; pl->c=c0; pl->mp=mp0;
    for(int s=0;s<steps;s++){
        pl->r += dr; pl->c += dc;

        /* cost on landing cell */
        touch_cell_costs(pl);
        if(pl->mp<=0){ visit_bawana(pl); return 2; }

        /* instant jumps (print here) */
        int guard=0;
        int changed=1;
        int seenCount=0; int seen[64][3];
        while(changed && guard++<64){
            changed=0;

            /* detect loop at the *start* of each iteration */
            for(int t=0;t<seenCount;t++){
                if(seen[t][0]==pl->f && seen[t][1]==pl->r && seen[t][2]==pl->c){
                    /* send to start area (keep MP) */
                    printf(" Player %c stuck in a stair/pole loop and sent to starting area\n", pl->id);
                    pl->in_maze=0; pl->f=0; pl->r=pl->home_r; pl->c=pl->home_c;
                    return 3;
                }
            }
            seen[seenCount][0]=pl->f; seen[seenCount][1]=pl->r; seen[seenCount][2]=pl->c; seenCount++;

            int beforeF=pl->f, beforeR=pl->r, beforeC=pl->c;
            maybe_take_pole(pl,1);
            maybe_take_stair(pl,1);
            if(pl->f!=beforeF || pl->r!=beforeR || pl->c!=beforeC){
                changed=1;
                touch_cell_costs(pl);
                if(pl->mp<=0){ visit_bawana(pl); return 2; }
                /* stepping onto start area via jump? -> treat as out of maze (keep MP) */
                if(shape[pl->f][pl->r][pl->c]==K_START){
                    printf(" %c fell into the starting area via a jump and must re-enter with a future six.\n", pl->id);
                    pl->in_maze=0; pl->f=0; pl->r=pl->home_r; pl->c=pl->home_c;
                    return 1; /* turn ends */
                }
            }
        }
    }
    /* disoriented countdown per throw */
    if(pl->disor>0){
        pl->disor--;
        if(pl->disor==0) printf("%c has recovered from disorientation.\n", pl->id);
    }
    return 1;
}

/* ---------- capture and win ---------- */
static void try_capture(Player *me){
    for(int i=0;i<3;i++){
        if(&P[i]==me) continue;
        if(P[i].in_maze && me->in_maze &&
           P[i].f==me->f && P[i].r==me->r && P[i].c==me->c){
            P[i].in_maze=0; P[i].f=0; P[i].r=P[i].home_r; P[i].c=P[i].home_c;
            printf("  %c captures %c -> %c back to start area\n", me->id, P[i].id, P[i].id);
        }
    }
}

static int same_pos(int f1,int r1,int c1,int f2,int r2,int c2){
    return f1==f2 && r1==r2 && c1==c2;
}

/* ---------- main ---------- */
int main(void){
    load_seed();

    build_shapes();   /* floor geometry */
    clear_effects();
    scatter_effects(); /* Rule 10 */
    build_bawana_layout();

    load_stairs();
    load_poles();
    apply_stair_through_walls(); /* clarification: simple approach */
    load_walls();

    load_flag();
    place_flag_randomly();

    setup_players();

    printf("========================================\n");
    printf("||           MAZE RUNNER               ||\n");
    printf("========================================\n\n");
    printf(" Flag placed at [Floor:%d, Row:%d, Col:%d]\n\n", flagF, flagR, flagC);

    int winner=-1;

    for(int round=1; round<=MAX_ROUNDS && winner==-1; ++round){
        printf("\n=========================== ROUND %d ==============================\n", round);

        if(round>1 && (round-1)%STAIR_TOGGLE_EVERY==0){
            stairMode = (rnd_int(0,1)? ST_UP : ST_DOWN);
            printf(" Stairs direction changed (global): now %s\n", (stairMode==ST_UP? "up":"down"));
        }

        for(int pi=0; pi<3 && winner==-1; ++pi){
            Player *pl=&P[pi];

            printf("\n--------------------------------------------------------------------\n");
            printf("│ Round %3d │ Player %c │ MP: %5d │ Pos: [%d,%d,%d] │ Dir: %s │\n",
                   round, pl->id, pl->mp, pl->f, pl->r, pl->c, dname(pl->dir));
            printf("--------------------------------------------------------------------\n");

            /* skipping because of food poisoning? */
            if(pl->skip>0){
                pl->skip--;
                printf("%c is still food poisoned and misses the turn.\n", pl->id);
                if(pl->skip==0){
                    printf("%c is now fit to proceed and will be assigned a new Bawana outcome.\n", pl->id);
                    visit_bawana(pl);
                }
                continue;
            }

            /* not in maze yet: must roll a 6 to enter */
            int move_die = rnd_int(1,6);

            if(!pl->in_maze){
                if(move_die==6){
                    pl->in_maze=1;
                    pl->dir=pl->start_dir;
                    pl->f=0; pl->r=pl->first_r; pl->c=pl->first_c;
                    printf("%c is at the starting area and rolls 6 on the movement dice and is placed on %d of the maze.\n",
                           pl->id, encode_cell(pl->f,pl->r,pl->c));
                    touch_cell_costs(pl); /* cost on that entry cell */
                }else{
                    printf("%c is at the starting area and rolls %d on the movement dice cannot enter the maze.\n",
                           pl->id, move_die);
                }
                continue;
            }

            /* in the maze: decide direction for this throw */
            int steps = move_die;
            int will_roll_dir = (pl->dirTick==3);
            pl->dirTick = (pl->dirTick+1)%4;

            if(pl->trig>0){ steps *= 2; pl->trig--; }

            if(pl->disor>0){
                /* walk_exact prints the "is disoriented and move ..." message */
            }else if(will_roll_dir){
                int face=rnd_int(1,6);
                int d2=dir_from_face(face);
                if(d2!=-1) pl->dir=d2;
                printf("%c rolls and %d on the movement dice and %s on the direction dice, changes direction to %s and moves %d cells and is now at ",
                       pl->id, (pl->trig? steps/2: steps), (d2==-1? "Empty":"some"), dname(pl->dir), steps);
            }else{
                printf("%c rolls and %d on the movement dice and moves %s by %d cells and is now at ",
                       pl->id, (pl->trig? steps/2: steps), dname(pl->dir), steps);
            }

            int beforeF=pl->f, beforeR=pl->r, beforeC=pl->c, beforeMP=pl->mp;

            int res = walk_exact(pl, steps);
            if(res==0){ /* blocked */
                printf("%d.\n", encode_cell(beforeF,beforeR,beforeC));
                printf("%c rolls and %d on the movement dice and cannot move in the %s. Player remains at %d\n",
                       pl->id, move_die, dname(pl->dir), encode_cell(beforeF,beforeR,beforeC));
                pl->mp = beforeMP; /* restore MP on failed attempt, then apply penalty */
                pl->mp -= PENALTY_BLOCKED;
                dbg_blocks++;
            }else if(res==3){
                /* loop: already printed, put Player in start area; keep MP */
                printf("%c moved 0 that cost %d movement points (block/loop handling) and is left with %d and is moving in the %s.\n",
                       pl->id, PENALTY_BLOCKED, pl->mp, dname(pl->dir));
            }else{
                printf("%d.\n", encode_cell(pl->f,pl->r,pl->c));
                int cells_moved = abs(pl->f-beforeF)*0 + abs(pl->r-beforeR)+abs(pl->c-beforeC); /* rough count (for message) */
                int cost = beforeMP - pl->mp;
                printf("%c moved %d that cost %d movement points and is left with %d and is moving in the %s.\n",
                       pl->id, cells_moved, (cost<0?0:cost), pl->mp, dname(pl->dir));
            }

            /* capture check (only if still in maze) */
            if(pl->in_maze) try_capture(pl);

            /* win check */
            if(pl->in_maze && same_pos(pl->f,pl->r,pl->c, flagF,flagR,flagC)){
                printf("\n===============================================================\n");
                printf(" Player %c captured the flag at [%d,%d,%d]!\n", pl->id, pl->f, pl->r, pl->c);
                printf(" GAME OVER in %d rounds!\n", round);
                printf("===============================================================\n");
                winner=pi;
            }
        }
    }

    if(winner==-1){
        printf("\nNo winner within %d rounds.\n", MAX_ROUNDS);
        printf(" Game ended.\n");
    }

    /* simple debug footer */
    printf("\nDEBUG: bawana visits=%d, stairs taken=%d, poles used=%d, blocked moves=%d\n",
           dbg_bawana, dbg_stairs, dbg_poles, dbg_blocks);
    return 0;
}
