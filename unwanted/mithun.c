#include <stdio.h>
#include <stdlib.h>
#include <time.h>

#define NUM_PLAYERS 3
#define MAZE_WIDTH 10
#define MAZE_LENGTH 25
#define FLOORS 3
#define MAX_STAIRS 7
#define MAX_WALLS 4
#define MAX_POLES 3

int reverseStairs = 0;

// Directions
enum { NORTH, EAST, SOUTH, WEST };

// Player structure
typedef struct {
    char name;
    int floor, x, y;     // position in maze
    int direction;       // movement direction
    int inMaze;          // 0 = not yet entered
    int diceCount;       // rolls since entering
    long long mp;        // movement points (using long long to prevent overflow)
    int blocked;             
} Player;

// Stair structure
typedef struct {
    int sFloor, sx, sy;
    int eFloor, ex, ey;
} Stair;

// Global stairs
Stair stairs[MAX_STAIRS] = {
    {0, 4, 5, 1, 3, 20},
    {0, 4, 15, 1, 7, 12},
    {0, 2, 20, 1, 8, 5},
    {1, 1, 5, 2, 0, 15},
    {1, 5, 18, 2, 3, 10},
    {1, 9, 22, 2, 4, 14},
    {0, 3, 24, 2, 9, 12}
};

//wall structure
typedef struct {
    int ssFloor,ssx,ssy;
    int eex,eey;
} Wall;

//initiating walls

Wall walls[MAX_WALLS]={
    {0,9,20,6,20},
    {0,6,21,6,24},
    {1,3,5,3,5},
    {1,3,11,3,12}
};

//Pole structure
typedef struct {
    int sssFloor,sssx,sssy;
    int eeeFloor,eeex,eeey;
} Pole;

//initiating Polls

Pole poles[MAX_POLES]={
    {2,1,12,0,1,12},
    {1,4,0,0,4,0},
    {1,2,22,0,2,22}
};

// Maze cells consumable values
int cellCost[FLOORS][MAZE_WIDTH][MAZE_LENGTH];
int cellMult[FLOORS][MAZE_WIDTH][MAZE_LENGTH];

// Player starting positions (outside maze)
const int startX[NUM_PLAYERS] = {6, 9, 9};
const int startY[NUM_PLAYERS] = {12, 8, 16};
const int startDir[NUM_PLAYERS] = {NORTH, WEST, EAST};

// Roll a dice (1–6)
int rollDice() {
    return rand() % 6 + 1;
}

// Move player one step in given direction
void moveStep(Player *p) {
    switch(p->direction) { // The inMaze check is redundant as this is only called for players in the maze
        case NORTH: if (p->x > 0) p->x--; break;
        case SOUTH: if (p->x < MAZE_WIDTH-1) p->x++; break;
        case EAST:  if (p->y < MAZE_LENGTH-1) p->y++; break;
        case WEST:  if (p->y > 0) p->y--; break;
    }
}

// Roll direction dice
void updateDirection(Player *p) {
    int d = rand() % 6 + 1;
    if (d == 2) p->direction = NORTH;
    else if (d == 3) p->direction = EAST;
    else if (d == 4) p->direction = SOUTH;
    else if (d == 5) p->direction = WEST;
    // Faces 1 & 6 = empty → keep same direction
}

// Check if player landed on stair
int checkStair(Player *p) {
    for (int i=0; i<MAX_STAIRS; i++) {
        if (!reverseStairs && p->floor == stairs[i].sFloor &&
            p->x == stairs[i].sx &&
            p->y == stairs[i].sy) {
            p->floor = stairs[i].eFloor;
            p->x = stairs[i].ex;
            p->y = stairs[i].ey;
            printf("Player %c enter to a stair and jump to Floor %d (%d,%d)\n",
                   p->name, p->floor, p->x, p->y);
            return 1;
        } else if (reverseStairs && p->floor == stairs[i].eFloor &&
            p->x == stairs[i].ex &&
            p->y == stairs[i].ey) {
            p->floor = stairs[i].sFloor;
            p->x = stairs[i].sx;
            p->y = stairs[i].sy;
            printf("Player %c enter to a reverse stair and jump to Floor %d (%d,%d)\n",
                   p->name, p->floor, p->x, p->y);
            return 1;
        }
        
    }
    return 0;
}


void checkWalls(Player *p) {
    for (int i = 0; i < MAX_WALLS; i++) {
        if (p->floor != walls[i].ssFloor) continue;

        // Vertical wall (x constant)
        if (walls[i].ssx == walls[i].eex && p->x == walls[i].ssx) {
            int minY = (walls[i].ssy < walls[i].eey) ? walls[i].ssy : walls[i].eey;
            int maxY = (walls[i].ssy > walls[i].eey) ? walls[i].ssy : walls[i].eey;
            if (p->y >= minY && p->y <= maxY) {
                p->blocked = 1;
                return;
            }
        } else if (walls[i].ssy == walls[i].eey && p->y == walls[i].ssy) { // Horizontal wall (y constant)
            int minX = (walls[i].ssx < walls[i].eex) ? walls[i].ssx : walls[i].eex;
            int maxX = (walls[i].ssx > walls[i].eex) ? walls[i].ssx : walls[i].eex;
            if (p->x >= minX && p->x <= maxX) {
                p->blocked = 1;
                return;
            }
        }
    }
}


// Check if player landed on poles
int checkPoles(Player *p) {
    for (int i=0; i<MAX_POLES; i++) {
        if ((p->floor == poles[i].sssFloor || p->floor == (poles[i].sssFloor - 1)) &&
            p->x == poles[i].sssx && p->y == poles[i].sssy) {
            p->floor = poles[i].eeeFloor;
            p->x = poles[i].eeex;
            p->y = poles[i].eeey;
            printf("Player %c entered to a Pole and jump to  %d (%d,%d)\n",
                   p->name, p->floor, p->x, p->y);
            return 1;
        }
    }
    return 0;
}

// Print player position
void printPos(Player p) {
    printf("Player %c -> Floor %d, (%d,%d), Dir=%d, MP=%lld\n",
           p.name, p.floor, p.x, p.y, p.direction, p.mp);
}
//Bawana
void bawana(Player *p,int playerIndex) {
    
}

// Reset a player to their starting state
void resetPlayer(Player *p, int playerIndex) {
    p->inMaze = 0;
    p->blocked = 0;
    p->floor = 0;
    p->x = startX[playerIndex];
    p->y = startY[playerIndex];
    p->direction = startDir[playerIndex];
    //p->mp = 100;
}

// Check if two players overlap -> capture
void checkCapture(Player players[], int currentIndex) {
    Player *p = &players[currentIndex];
    for (int j = 0; j < NUM_PLAYERS; j++) {
        if (j == currentIndex) continue; // skip self
        if (players[j].inMaze && players[j].floor == p->floor && players[j].x == p->x && players[j].y == p->y) {
            // Capture happens
            printf(">>> Player %c captured Player %c! %c sent back to START <<<\n", p->name, players[j].name, players[j].name);
            resetPlayer(&players[j], j);
        }
    }
}


// Initialize maze cell consumable values randomly
void initCellCosts() {
    for (int f = 0; f < FLOORS; f++) {
        for (int x = 0; x < MAZE_WIDTH; x++) {
            for (int y = 0; y < MAZE_LENGTH; y++) {
                // Check if the current cell is in a restricted zone
                if ((f == 0 && (x > 5 && x < 10) && (y > 7 && y < 17)) ||
                    (f == 1 && (x >= 0 && x < 5) && (y >= 7 && y < 17)) ||
                    (f == 2 && ((y >= 0 && y < 8) || (y > 16 && y <= 24)))) {
                    // It's a restricted zone, so assign default "safe" values and continue.
                    cellCost[f][x][y] = 0;
                    cellMult[f][x][y] = 1;
                    continue;
                }

                // This code now runs for all non-restricted cells.
                int roll = rand() % 100;
                cellMult[f][x][y] = 1; // Default multiplier is 1

                if (roll < 25) {
                    cellCost[f][x][y] = 0;
                } else if (roll < 60) {
                    cellCost[f][x][y] = (rand() % 4) + 1;
                } else if (roll < 85) {
                    cellCost[f][x][y] = (rand() % 2) - 2;
                } else if (roll < 95) {
                    cellCost[f][x][y] = (rand() % 2) - 4;
                } else {
                    // 5% of cells are bonus cells
                    cellCost[f][x][y] = 0; // No cost for bonus cells
                    cellMult[f][x][y] = (rand() % 2) + 2; // Multiplier of 2 or 3
                }
            }
        }
    }
}

// Helper function for flag placement: checks if coords are in a restricted zone
int isPositionInRestrictedZone(int f, int x, int y) {
    if (f == 0 && (x > 5 && x < 10) && (y > 7 && y < 17)) return 1;
    if (f == 1 && (x >= 0 && x < 5) && (y >= 7 && y < 17)) return 1;
    if (f == 2 && ((y >= 0 && y < 8) || (y > 16 && y <= 24))) return 1;
    return 0;
}

// Helper function for flag placement: checks if coords are on a stair
int isPositionOnStair(int f, int x, int y) {
    for (int i = 0; i < MAX_STAIRS; i++) {
        if ((f == stairs[i].sFloor && x == stairs[i].sx && y == stairs[i].sy) ||
            (f == stairs[i].eFloor && x == stairs[i].ex && y == stairs[i].ey)) {
            return 1;
        }
    }
    return 0;
}

// Helper function for flag placement: checks if coords are on a pole
int isPositionOnPole(int f, int x, int y) {
    for (int i = 0; i < MAX_POLES; i++) {
        if ((f == poles[i].sssFloor && x == poles[i].sssx && y == poles[i].sssy) ||
            (f == poles[i].eeeFloor && x == poles[i].eeex && y == poles[i].eeey)) {
            return 1;
        }
    }
    return 0;
}

// Helper function for flag placement: checks if coords are on a wall
int isPositionOnWall(int f, int x, int y) {
    for (int i = 0; i < MAX_WALLS; i++) {
        if (f != walls[i].ssFloor) continue;

        if (walls[i].ssx == walls[i].eex && x == walls[i].ssx) { // Vertical wall
            int minY = (walls[i].ssy < walls[i].eey) ? walls[i].ssy : walls[i].eey;
            int maxY = (walls[i].ssy > walls[i].eey) ? walls[i].ssy : walls[i].eey;
            if (y >= minY && y <= maxY) return 1;
        } else if (walls[i].ssy == walls[i].eey && y == walls[i].ssy) { // Horizontal wall
            int minX = (walls[i].ssx < walls[i].eex) ? walls[i].ssx : walls[i].eex;
            int maxX = (walls[i].ssx > walls[i].eex) ? walls[i].ssx : walls[i].eex;
            if (x >= minX && x <= maxX) return 1;
        }
    }
    return 0;
}

void randomizeFlagPosition(int *flagFloor, int *flagX, int *flagY) {
    while (1) {
        *flagFloor = rand() % FLOORS;
        *flagX = rand() % MAZE_WIDTH;
        *flagY = rand() % MAZE_LENGTH;

        if (isPositionInRestrictedZone(*flagFloor, *flagX, *flagY)) continue;
        if (isPositionOnStair(*flagFloor, *flagX, *flagY)) continue;
        if (isPositionOnPole(*flagFloor, *flagX, *flagY)) continue;
        if (isPositionOnWall(*flagFloor, *flagX, *flagY)) continue;

        // If we reach here, the position is valid
        break;
    }
}

// Check if player is in a hard-coded restricted zone
int isPlayerInRestrictedZone(Player *p) {
    if (p->floor == 0 && (p->x > 5 && p->x < 10) && (p->y > 7 && p->y < 17)) {
        return 1;
    }
    if (p->floor == 1 && (p->x >= 0 && p->x < 5) && (p->y >= 7 && p->y < 17)) {
        return 1;
    }
    if (p->floor == 2 && ((p->y >= 0 && p->y < 8) || (p->y > 16 && p->y <= 24))) {
        return 1;
    }
    return 0;
}

int main() {
    srand(time(NULL));
    initCellCosts();

    // Initialize players A,B,C
    Player players[NUM_PLAYERS] = {
        {.name = 'A', .floor = 0, .x = startX[0], .y = startY[0], .direction = startDir[0], .inMaze = 0, .diceCount = 0, .mp = 100, .blocked = 0},
        {.name = 'B', .floor = 0, .x = startX[1], .y = startY[1], .direction = startDir[1], .inMaze = 0, .diceCount = 0, .mp = 100, .blocked = 0},
        {.name = 'C', .floor = 0, .x = startX[2], .y = startY[2], .direction = startDir[2], .inMaze = 0, .diceCount = 0, .mp = 100, .blocked = 0}
    };

    // First cell to enter for each
    int enterX[NUM_PLAYERS] = {5, 9, 9};
    int enterY[NUM_PLAYERS] = {12, 7, 17};

    int flagFloor, flagX, flagY;
    randomizeFlagPosition(&flagFloor, &flagX, &flagY);
    printf("The flag has been placed at Floor %d, (%d, %d)\n", flagFloor, flagX, flagY);

    int gameOver = 0, round = 1;

    while (!gameOver) {
        printf("\n--- Round %d ---\n", round++);
        for (int i=0; i<NUM_PLAYERS; i++) {
            Player *p = &players[i];
            if (p->mp <= 0) {
                printf("Player %c is out of MP and sent back to START.\n", p->name);
                resetPlayer(p, i);
                continue;
            }

            int moveDice = rollDice();
            p->diceCount++;

            printf("Player %c rolled %d (MP=%lld)\n", p->name, moveDice, p->mp);

            if (!p->inMaze) {
                if (moveDice == 6) {
                    p->x = enterX[i];
                    p->y = enterY[i];
                    p->inMaze = 1;
                    printf("Player %c ENTERS maze at (%d,%d)\n", p->name, p->x, p->y);
                } else {
                    printf("Player %c stays outside maze.\n", p->name);
                }
            } else {
                // Every 4th throw includes direction dice
                if (p->diceCount % 4 == 0) {
                    updateDirection(p);
                }
                if(round % 5 == 0){
                    reverseStairs=1;
                }else{
                    reverseStairs=0;
                }
                
                int oldX=p->x;
                int oldY=p->y;
                int oldFloor=p->floor;
                int oldMp = p->mp; // Save the MP before the move

                for (int m=0; m<moveDice; m++) {
                    moveStep(p);

                    // Reset blocked status for this step and check for invalid moves
                    p->blocked = 0;
                    if (isPlayerInRestrictedZone(p)) p->blocked = 1;
                    checkWalls(p);

                    if(p->blocked == 1){
                        printf("Player %c was blocked! Restarting move.\n", p->name);
                        p->x=oldX;
                        p->y=oldY;
                        p->floor=oldFloor;
                        p->mp = oldMp; // Restore the MP

                        updateDirection(p);
                        p->blocked = 0;
                        m=-1; // Restart the entire move sequence for this turn
                        continue;
                    }

                    // If the step is valid, deduct MP and then check for teleports
                    p->mp -= cellCost[p->floor][p->x][p->y];

                    // Apply multiplier on every step
                    if (p->mp > 0) {
                        int multiplier = cellMult[p->floor][p->x][p->y];
                        if (multiplier > 1) {
                            p->mp *= multiplier;
                            printf("Player %c hit a bonus mid-move! MP x%d -> %lld\n", p->name, multiplier, p->mp);
                        }
                    }

                    if (p->mp <= 0) break;

                    if (checkStair(p)) continue; // If teleported, skip pole check for this step
                    checkPoles(p);
                } // <-- This brace correctly closes the for-loop for movement

                checkCapture(players, i);
                printPos(*p);

                if (round == 1001) {
                    gameOver = 1;
                }


                // Check flag
                if (p->floor == flagFloor && p->x == flagX && p->y == flagY) {
                    printf(">>> Player %c captured the FLAG! <<<\n", p->name);
                    gameOver = 1;
                    break;
                }
            }
        }
    }

    return 0;
}