#include <stdio.h>
#include <stdlib.h>
#include <time.h>

#define FLOORS 3
#define ROWS 10      /* 0..9  (width)  */
#define COLS 25      /* 0..24 (length) */

/* --- cell kinds --- */
#define K_VOID   -1  /* not a real cell on that floor's shape */
#define K_FREE    0  /* normal maze cell */
#define K_START   1  /* starting area (treated as blocked for movement) */
#define K_bawana  2  /* Bawana 4x4 area (not enterable by walking) */
#define K_WALL    3  /* blocking wall */

/* --- directions --- */
#define DIR_N 0
#define DIR_E 1
#define DIR_S 2
#define DIR_W 3

/* game knobs (from brief) */
#define MAX_ROUNDS 1000
#define MP_INIT 100
#define PENALTY_BLOCKED 2
#define STAIR_TOGGLE_EVERY 5

/* bawana (Bawana) area on floor 0 */
#define BW_RMIN 6
#define BW_RMAX 9
#define BW_CMIN 20
#define BW_CMAX 23
/* one-way exit just outside, facing north (floor 0, row 9, col 19) */
#define EXIT_ROW 9
#define EXIT_COL 19

/* start area on floor 0: rows 6..9, cols 8..16 */
#define ST_RMIN 6
#define ST_RMAX 9
#define ST_CMIN 8
#define ST_CMAX 16

/* floor shapes (brief):
   - floor 0: full 10x25
   - floor 1: two rectangles (cols 0..7 and 16..23) plus bridge rows 6..9 cols 8..16
   - floor 2: rectangle cols 8..16
*/

/* direction die lasts: disoriented 4 throws (explicit in brief), trigger length not
   specified, we use 4 throws to keep it gameplay-meaningful and symmetric. */
#define DISORIENTED_TURNS 4
#define TRIG_TURNS 4

/* bawana cell kinds */
#define E_POINTS 0
#define E_FOOD   1
#define E_DISORIENTED  2
#define E_TRIG   3
#define E_HAPPY  4

/* distributions (Rule 10) */
#define CONSUME_0_PCT 25
#define CONSUME_14_PCT 35
#define BONUS_12_PCT 25
#define BONUS_35_PCT 10
/* remainder -> multipliers x2/x3 */

/* global stair mode */
#define ST_UP   0  /* only from lower floor endpoint to higher floor endpoint */
#define ST_DOWN 1  /* only from higher floor endpoint to lower floor endpoint */

/* simple player struct */
typedef struct {
    char id;     /* 'A','B','C' */
    int f, r, c; /* position */
    int in_maze; /* 0 not in, 1 in */
    int dir;     /* DIR_* */
    int mp;      /* movement points */
    int skip;    /* food poisoning turns to skip */
    int disoriented;   /* disoriented throws left */
    int trig;    /* triggered throws left (double steps) */
    int dirTick; /* 0..3 (roll direction die together with movement on tick==3) */

    /* fixed starts from brief */
    int home_r, home_c;   /* in start area */
    int first_r, first_c; /* first maze cell when they roll a 6 to enter */
} Pawn;

/* stairs and poles kept very plain */
typedef struct {
    int aF,aR,aC; /* lower endpoint floor/row/col (we will compute which is lower) */
    int bF,bR,bC; /* upper endpoint */
} Ladder;

typedef struct {
    int lowF, topF, r, c; /* same r,c across floors; drop to lowF when above it */
} Pole;

/* ---------- globals kept intentionally simple ---------- */
static int shape[FLOORS][ROWS][COLS];   /* K_* (or K_VOID) */
static int consm[FLOORS][ROWS][COLS];   /* -1 none, else 0..4 consumable */
static int plusp[FLOORS][ROWS][COLS];   /* +bonus (0..5) */
static int multp[FLOORS][ROWS][COLS];   /* xbonus (1,2,3) */

static int bawana[4][4];                /* 4x4 types: E_* */
static Pawn P[3];
static int flagF, flagR, flagC;

static Ladder stairs[6];                /* a small, fixed set (coords differ from yours) */
static int stairMode = ST_UP;
static Pole poles[2];                   /* small, fixed set (coords differ from yours) */

/* ---------- helpers ---------- */
static int rnd(int a, int b){ return a + rand() % (b - a + 1); }

static int in_bounds(int r, int c){ return r>=0 && r<ROWS && c>=0 && c<COLS; }

static int is_free_cell(int f, int r, int c){
    if(!in_bounds(r,c)) return 0;
    return shape[f][r][c]==K_FREE;
}

/* get step deltas with a switch (avoid direction arrays for “beginner” vibe) */
static void step_delta(int d, int *dr, int *dc){
    *dr = 0; *dc = 0;
    switch(d){
        case DIR_N: *dr = -1; *dc =  0; break;
        case DIR_E: *dr =  0; *dc =  1; break;
        case DIR_S: *dr =  1; *dc =  0; break;
        case DIR_W: *dr =  0; *dc = -1; break;
        default: break;
    }
}

/* direction die mapping (Rule 2) */
static int dir_from_face(int face){
    if(face==2) return DIR_N;
    if(face==3) return DIR_E;
    if(face==4) return DIR_S;
    if(face==5) return DIR_W;
    return -1; /* 1 or 6 are empty -> keep current */
}

/* ---------- board construction ---------- */
static void make_floor_shapes(void){
    /* default everything to void */
    for(int f=0; f<FLOORS; ++f)
        for(int r=0; r<ROWS; ++r)
            for(int c=0; c<COLS; ++c)
                shape[f][r][c] = K_VOID;

    /* floor 0: full */
    for(int r=0;r<ROWS;++r)
    for(int c=0;c<COLS;++c)
        shape[0][r][c] = K_FREE;

    /* start area (blocked for walking) */
    for(int r=ST_RMIN;r<=ST_RMAX;++r)
    for(int c=ST_CMIN;c<=ST_CMAX;++c)
        shape[0][r][c] = K_START;

    /* Bawana cells (not enterable by walking) */
    for(int r=BW_RMIN;r<=BW_RMAX;++r)
    for(int c=BW_CMIN;c<=BW_CMAX;++c)
        shape[0][r][c] = K_bawana;

    /* Bawana walls per brief:
       horizontal (6,20..24) and vertical (6..9,20) — entrance at (9,19). */
    for(int c=20;c<=24;++c) shape[0][6][c] = K_WALL;
    for(int r=6; r<=9; ++r) shape[0][r][20] = K_WALL;

    /* floor 1 (two rectangles + bridge) */
    for(int r=0;r<ROWS;++r){
        for(int c=0;c<=7;++c)  shape[1][r][c]  = K_FREE; /* left block */
        for(int c=16;c<=23;++c) shape[1][r][c] = K_FREE; /* right block */
    }
    for(int r=6;r<=9;++r)
        for(int c=8;c<=16;++c)
            shape[1][r][c] = K_FREE; /* bridge rows */

    /* floor 2 (cols 8..16) */
    for(int r=0;r<ROWS;++r)
        for(int c=8;c<=16;++c)
            shape[2][r][c] = K_FREE;
}

static void clear_cell_effects(void){
    for(int f=0; f<FLOORS; ++f)
    for(int r=0; r<ROWS; ++r)
    for(int c=0; c<COLS; ++c){
        consm[f][r][c] = -1;
        plusp[f][r][c] = 0;
        multp[f][r][c] = 1;
    }
}

static void scatter_consumables_and_bonuses(void){
    /* collect all K_FREE cells */
    int cap = FLOORS*ROWS*COLS;
    int (*list)[3] = malloc(sizeof(int)*3*cap);
    int n = 0;
    for(int f=0; f<FLOORS; ++f)
    for(int r=0; r<ROWS; ++r)
    for(int c=0; c<COLS; ++c)
        if (shape[f][r][c]==K_FREE) { list[n][0]=f; list[n][1]=r; list[n][2]=c; ++n; }

    /* counts per Rule 10 */
    int con0 = (CONSUME_0_PCT * n)/100;
    int con14 = (CONSUME_14_PCT * n)/100;
    int add12 = (BONUS_12_PCT * n)/100;
    int add35 = (BONUS_35_PCT * n)/100;
    int mul23 = n - (con0 + con14 + add12 + add35);

    /* helper to pick distinct cells that still have default effects */
    int placed = 0;
    int tries = 0;

    /* consumable 0 */
    placed=0; tries=0;
    while(placed<con0 && tries<10*n){
        int i = rnd(0, n-1);
        int f=list[i][0], r=list[i][1], c=list[i][2];
        if (consm[f][r][c]==-1 && plusp[f][r][c]==0 && multp[f][r][c]==1){
            consm[f][r][c]=0;
            placed++;
        }
        tries++;
    }
    /* consumable 1..4 */
    placed=0; tries=0;
    while(placed<con14 && tries<10*n){
        int i=rnd(0,n-1);
        int f=list[i][0], r=list[i][1], c=list[i][2];
        if (consm[f][r][c]==-1 && plusp[f][r][c]==0 && multp[f][r][c]==1){
            consm[f][r][c]=rnd(1,4);
            placed++;
        }
        tries++;
    }
    /* add 1..2 */
    placed=0; tries=0;
    while(placed<add12 && tries<10*n){
        int i=rnd(0,n-1);
        int f=list[i][0], r=list[i][1], c=list[i][2];
        if (consm[f][r][c]==-1 && plusp[f][r][c]==0 && multp[f][r][c]==1){
            plusp[f][r][c]=rnd(1,2);
            placed++;
        }
        tries++;
    }
    /* add 3..5 */
    placed=0; tries=0;
    while(placed<add35 && tries<10*n){
        int i=rnd(0,n-1);
        int f=list[i][0], r=list[i][1], c=list[i][2];
        if (consm[f][r][c]==-1 && plusp[f][r][c]==0 && multp[f][r][c]==1){
            plusp[f][r][c]=rnd(3,5);
            placed++;
        }
        tries++;
    }
    /* mul x2,x3 */
    placed=0; tries=0;
    while(placed<mul23 && tries<10*n){
        int i=rnd(0,n-1);
        int f=list[i][0], r=list[i][1], c=list[i][2];
        if (consm[f][r][c]==-1 && plusp[f][r][c]==0 && multp[f][r][c]==1){
            multp[f][r][c]=rnd(2,3);
            placed++;
        }
        tries++;
    }
    free(list);
}

static void make_bawana_layout(void){
    /* 16 cells: 3 each of FOOD/DISORIENTED/TRIG/HAPPY (12 total) + 4 POINTS */
    int bag[16], k=0;
    for(int i=0;i<3;i++) bag[k++]=E_FOOD;
    for(int i=0;i<3;i++) bag[k++]=E_DISORIENTED;
    for(int i=0;i<3;i++) bag[k++]=E_TRIG;
    for(int i=0;i<3;i++) bag[k++]=E_HAPPY;
    for(int i=0;i<4;i++) bag[k++]=E_POINTS;
    /* shuffle and fill */
    for(int i=15;i>0;i--){
        int j=rnd(0,i);
        int t=bag[i]; bag[i]=bag[j]; bag[j]=t;
    }
    k=0;
    for(int rr=0; rr<4; ++rr)
        for(int cc=0; cc<4; ++cc)
            bawana[rr][cc] = bag[k++];
}

static void place_flag_randomly(void){
    while(1){
        int f=rnd(0,2), r=rnd(0,9), c=rnd(0,24);
        if (shape[f][r][c]==K_FREE){
            flagF=f; flagR=r; flagC=c;
            break;
        }
    }
}

/* stairs/poles: define a few fixed ones (these numbers are DIFFERENT from your file) */
static void make_stairs_and_poles(void){
    /* stairs (lower -> upper) */
    stairs[0]=(Ladder){0,4,6, 2,0,12};
    stairs[1]=(Ladder){0,3,18, 1,8,7};
    stairs[2]=(Ladder){0,2,9,  1,6,8};
    stairs[3]=(Ladder){1,1,20, 2,5,9};
    stairs[4]=(Ladder){0,9,2,  1,9,16};
    stairs[5]=(Ladder){1,6,0,  2,2,13};

    /* poles (lowF, topF, r, c) -> drop to lowF when above it at the same r,c */
    poles[0]=(Pole){0,2,4,12};
    poles[1]=(Pole){0,1,7,6};
}

/* ---------- player setup ---------- */
static void setup_players(void){
    /* From brief Rule 1: starting cells + first maze cell + initial direction. */
    P[0].id='A'; P[0].home_r=6; P[0].home_c=12; P[0].first_r=5; P[0].first_c=12; P[0].dir=DIR_N; /* A north */
    P[1].id='B'; P[1].home_r=9; P[1].home_c=8;  P[1].first_r=9; P[1].first_c=7;  P[1].dir=DIR_W; /* B west  */
    P[2].id='C'; P[2].home_r=9; P[2].home_c=16; P[2].first_r=9; P[2].first_c=17; P[2].dir=DIR_E; /* C east  */

    for(int i=0;i<3;i++){
        P[i].f=0; P[i].r=P[i].home_r; P[i].c=P[i].home_c;
        P[i].in_maze=0;
        P[i].mp=MP_INIT;
        P[i].skip=0; P[i].disoriented=0; P[i].trig=0; P[i].dirTick=0;
    }
}

/* ---------- bawana (Bawana) handling ---------- */
static void go_to_exit(Pawn *pl){
    pl->f=0; pl->r=EXIT_ROW; pl->c=EXIT_COL; pl->dir=DIR_N; pl->in_maze=1;
}

static void visit_bawana(Pawn *pl){
    /* pick random 4x4 cell */
    int rr=rnd(0,3), cc=rnd(0,3);
    int kind=bawana[rr][cc];

    if(kind==E_POINTS){
        int gain=rnd(10,100);
        pl->mp += gain;
        go_to_exit(pl);
        printf("  %c bawana (points +%d) → exit at [0,%d,%d], MP=%d\n",
               pl->id,gain,EXIT_ROW,EXIT_COL,pl->mp);
    }else if(kind==E_FOOD){
        pl->skip = 3; /* miss 3 throws, then re-enter bawana again (per brief wording) */
        printf("  %c food poisoning (skip 3 turns)\n", pl->id);
        /* they stay “out of play” until skip counts down; on expiry we visit bawana again */
        pl->in_maze = 0;
        pl->f=0; pl->r=pl->home_r; pl->c=pl->home_c;
    }else if(kind==E_DISORIENTED){
        pl->mp += 50;
        pl->disoriented = DISORIENTED_TURNS;
        go_to_exit(pl);
        printf("  %c disoriented (+50 MP, random dirs for %d throws) → exit, MP=%d\n",
               pl->id,DISORIENTED_TURNS,pl->mp);
    }else if(kind==E_TRIG){
        pl->mp += 50;
        pl->trig = TRIG_TURNS;
        go_to_exit(pl);
        printf("  %c triggered (+50 MP, double steps for %d throws) → exit, MP=%d\n",
               pl->id,TRIG_TURNS,pl->mp);
    }else if(kind==E_HAPPY){
        pl->mp += 200;
        go_to_exit(pl);
        printf("  %c happy (+200 MP) → exit, MP=%d\n", pl->id,pl->mp);
    }
}

/* ---------- stairs/poles effects while walking ---------- */
static int at_same_cell(int f,int r,int c,int F,int R,int C){
    return (f==F && r==R && c==C);
}

static void maybe_take_pole(Pawn *pl){
    for(int i=0;i<2;i++){
        Pole t=poles[i];
        if(pl->r==t.r && pl->c==t.c && pl->f>t.lowF && pl->f<=t.topF){
            pl->f = t.lowF; /* drop down */
        }
    }
}
static void maybe_take_stair(Pawn *pl){
    for(int i=0;i<6;i++){
        Ladder L=stairs[i];
        int lowF = (L.aF < L.bF)? L.aF : L.bF;
        int upF  = (L.aF < L.bF)? L.bF : L.aF;
        int lr   = (L.aF < L.bF)? L.aR : L.bR;
        int lc   = (L.aF < L.bF)? L.aC : L.bC;
        int ur   = (L.aF < L.bF)? L.bR : L.bR; /* both ways use bR, but we remap below */
        int uc   = (L.aF < L.bF)? L.bC : L.bC;
        /* remap rows/cols for clarity: lower endpoint is (lowF,lr,lc), upper is (upF,ur,uc) */
        if(L.aF > L.bF){ /* swapped case: fix rows/cols */
            lr = L.bR; lc = L.bC; ur = L.aR; uc = L.aC;
        }

        if(stairMode==ST_UP){
            if(at_same_cell(pl->f,pl->r,pl->c,lowF,lr,lc)){
                pl->f=upF; pl->r=ur; pl->c=uc;
            }
        }else{ /* ST_DOWN */
            if(at_same_cell(pl->f,pl->r,pl->c,upF,ur,uc)){
                pl->f=lowF; pl->r=lr; pl->c=lc;
            }
        }
    }
}

/* apply cell effects while passing through a free cell (consumable/bonus) */
static void touch_cell_costs(Pawn *pl){
    int f=pl->f, r=pl->r, c=pl->c;
    int use = consm[f][r][c];
    int add = plusp[f][r][c];
    int mul = multp[f][r][c];

    /* simple order: subtract consumable, then add, then multiply */
    if(use>=0) pl->mp -= use;
    if(add>0)  pl->mp += add;
    if(mul>1)  pl->mp = pl->mp * mul;
}

/* Try to walk exactly 'steps'. Return:
   0 = blocked (no move, caller will charge PENALTY_BLOCKED)
   1 = moved normally
   2 = MP exhausted during movement -> sent to bawana (handled here), skip capture/flag this turn
*/
static int walk_exact(Pawn *pl, int steps){
    int f0=pl->f, r0=pl->r, c0=pl->c, mp0=pl->mp;
    int dr=0, dc=0;

    /* direction for this throw: if DISORIENTED, pick a random direction for the WHOLE throw */
    int use_dir = pl->dir;
    if(pl->disoriented>0) use_dir = rnd(0,3);
    step_delta(use_dir,&dr,&dc);

    /* simulate first to see if the entire path is valid (walls/edges/start/bawana blocked) */
    int tf=f0, tr=r0, tc=c0;
    for(int s=0;s<steps;s++){
        int nr = tr+dr, nc = tc+dc;
        if(!in_bounds(nr,nc)) return 0;
        int kind = shape[tf][nr][nc];
        if(kind!=K_FREE){ /* no entering start/bawana/walls by walking */
            return 0;
        }
        /* ok to move one; also consider stairs/poles “in the middle” (Rule 4) */
        tr=nr; tc=nc;
        /* for the validity dry-run, we must also jump if there is a stair/pole mid-way.
           That means we need a copy of the logic: we update tf,tr,tc as if stepping. */
        Pawn fake=*pl; fake.f=tf; fake.r=tr; fake.c=tc;
        maybe_take_pole(&fake);
        maybe_take_stair(&fake);
        tf=fake.f; tr=fake.r; tc=fake.c;
    }

    /* Actually perform the movement now, applying costs and jumps and MP checks */
    pl->f=f0; pl->r=r0; pl->c=c0; pl->mp=mp0;
    for(int s=0;s<steps;s++){
        pl->r += dr; pl->c += dc;
        /* apply costs on the cell we just moved into */
        touch_cell_costs(pl);
        /* instant jumps if any (then apply costs on the landing cell as well) */
        maybe_take_pole(pl);
        maybe_take_stair(pl);
        touch_cell_costs(pl);

        if(pl->mp<=0){
            printf("  %c MP<=0 → sent to bawana\n", pl->id);
            visit_bawana(pl);
            return 2;
        }
    }
    /* keep the player's canonical direction updated (for non-DISORIENTED throws) */
    if(pl->disoriented>0) pl->disoriented--; /* DISORIENTED counts down per throw */
    return 1;
}

/* capture someone if you end on them (not yourself) */
static void try_capture(Pawn *me){
    for(int i=0;i<3;i++){
        Pawn *q=&P[i];
        if(q==me) continue;
        if(q->in_maze && me->in_maze && q->f==me->f && q->r==me->r && q->c==me->c){
            q->in_maze=0;
            q->f=0; q->r=q->home_r; q->c=q->home_c;
            printf("  %c captures %c → %c back to start area\n", me->id, q->id, q->id);
        }
    }
}

/* ---------- main gameplay ---------- */
int main(void){
    srand((unsigned)time(NULL));

    make_floor_shapes();
    clear_cell_effects();
    scatter_consumables_and_bonuses();
    make_bawana_layout();
    make_stairs_and_poles();
    place_flag_randomly();
    setup_players();

    printf("MAZE RUNNER)\n");
    printf("Flag at [%d,%d,%d]\n\n", flagF, flagR, flagC);

    int winner=-1;

    for(int round=1; round<=MAX_ROUNDS && winner==-1; ++round){
        if(round%STAIR_TOGGLE_EVERY==0){
            stairMode = (rnd(0,1)==0 ? ST_UP : ST_DOWN);
            printf("== stair mode now %s ==\n", stairMode==ST_UP?"UP":"DOWN");
        }

        for(int turn=0; turn<3 && winner==-1; ++turn){
            Pawn *pl = &P[turn];
            printf("R#%d  P:%c MP=%d pos=[%d,%d,%d] dir=%c\n",
                round, pl->id, pl->mp, pl->f, pl->r, pl->c,
                (pl->dir==DIR_N?'N':pl->dir==DIR_E?'E':pl->dir==DIR_S?'S':'W'));

            if(pl->skip>0){
                pl->skip--;
                printf("  skipping (food poisoning), left=%d\n", pl->skip);
                if(pl->skip==0){
                    /* after 3 misses, visit bawana again (as described) */
                    visit_bawana(pl);
                }
                continue;
            }

            int move_die = rnd(1,6);
            printf("  move die = %d\n", move_die);

            if(!pl->in_maze){
                if(move_die==6){
                    pl->f=0; pl->r=pl->first_r; pl->c=pl->first_c;
                    pl->in_maze=1;
                    printf("  enters maze at [0,%d,%d]\n", pl->r, pl->c);
                    /* do NOT change dirTick here; movement starts next time */
                }else{
                    continue;
                }
            }else{
                /* direction die every 4th throw (together with movement die) */
                if(pl->disoriented==0){ /* disoriented ignores direction die */
                    if(pl->dirTick==3){
                        int face = rnd(1,6);
                        int nd = dir_from_face(face);
                        if(nd!=-1){ pl->dir = nd; }
                        printf("  dir die = %d (%s)\n", face, nd==-1?"keep":"change");
                        pl->dirTick=0;
                    }else{
                        pl->dirTick++;
                    }
                }

                int steps = move_die;
                if(pl->trig>0){ steps *= 2; pl->trig--; }

                int res = walk_exact(pl, steps);
                if(res==0){
                    pl->mp -= PENALTY_BLOCKED;
                    printf("  blocked → -%d MP (now %d)\n", PENALTY_BLOCKED, pl->mp);
                    if(pl->mp<=0){
                        printf("  MP<=0 after penalty → bawana\n");
                        visit_bawana(pl);
                    }
                }else if(res==1){
                    try_capture(pl);
                    if(pl->in_maze && pl->f==flagF && pl->r==flagR && pl->c==flagC){
                        winner=turn;
                        printf("\n*** %c got the flag at [%d,%d,%d] in round %d ***\n",
                               pl->id, flagF, flagR, flagC, round);
                    }
                }else if(res==2){
                    /* already handled by visit_bawana; nothing else this turn */
                }
            }
        }
    }

    if(winner==-1){
        printf("\nNo winner within %d rounds.\n", MAX_ROUNDS);
    }
    return 0;
}
